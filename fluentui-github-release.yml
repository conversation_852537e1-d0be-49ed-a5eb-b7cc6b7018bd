trigger: none
name: $(Date:yyyyMMdd).$(Rev:r)

# Pipeline parameters
parameters:
- name: releaseVersion
  displayName: 'Release Version'
  type: string
  default: ''
  
- name: buildId
  displayName: 'Source Build ID'
  type: string
  default: ''

resources:
  pipelines:
  - pipeline: 'fluentui-android-maven-publish'
    project: 'ISS'
    source: 'fluentui-maven-central-publish [1es-pt]'
  repositories:
  - repository: OfficePipelineTemplates
    type: git
    name: 1ESPipelineTemplates/OfficePipelineTemplates
    ref: refs/tags/release

extends:
  template: v1/Office.Official.PipelineTemplate.yml@OfficePipelineTemplates
  parameters:
    pool:
      name: Azure-Pipelines-1ESPT-ExDShared
      image: windows-2022
      os: windows
    customBuildTags:
    - ES365AIMigrationTooling-Release
    stages:
    - stage: Stage_1
      displayName: GitHub Release
      jobs:
      - job: Job_1
        displayName: Create GitHub Release
        condition: succeeded()
        timeoutInMinutes: 0
        templateContext:
          type: releaseJob
          isProduction: true
          inputs:
          # Download the dogfood APK artifact
          - input: pipelineArtifact
            buildType: 'specific'
            project: 'ISS'
            definition: 'fluentui-maven-central-publish [1es-pt]'
            buildVersionToDownload: 'specific'
            pipelineId: '${{ parameters.buildId }}'
            artifactName: 'dogfood'
            targetPath: '$(Pipeline.Workspace)/fluentui-android-maven-publish/dogfood'
          # Download the release notes artifact
          - input: pipelineArtifact
            buildType: 'specific'
            project: 'ISS'
            definition: 'fluentui-maven-central-publish [1es-pt]'
            buildVersionToDownload: 'specific'
            pipelineId: '${{ parameters.buildId }}'
            artifactName: 'notes'
            targetPath: '$(Pipeline.Workspace)/fluentui-android-maven-publish/notes'
        steps:
        # Read release notes content
        - task: PowerShell@2
          displayName: 'Read Release Notes'
          inputs:
            targetType: 'inline'
            script: |
              $releaseNotesContent = Get-Content -Path "$(Pipeline.Workspace)/fluentui-android-maven-publish/notes/dogfood-release-notes.txt" -Raw
              Write-Host "##vso[task.setvariable variable=ReleaseNotesContent]$releaseNotesContent"

        # Create GitHub Release
        - task: GitHubRelease@1
          displayName: 'Create GitHub Release'
          inputs:
            gitHubConnection: 'GitHub-FluentUI-Android'
            repositoryName: 'microsoft/fluentui-android'
            action: 'create'
            target: '$(Build.SourceVersion)'
            tagSource: 'userSpecifiedTag'
            tag: 'v${{ parameters.releaseVersion }}'
            title: 'FluentUI Android v${{ parameters.releaseVersion }}'
            releaseNotesSource: 'inline'
            releaseNotesInline: '$(ReleaseNotesContent)'
            assets: '$(Pipeline.Workspace)/fluentui-android-maven-publish/dogfood/FluentUI.Demo-dogfood-release.apk'
            addChangeLog: false
            isPreRelease: false
